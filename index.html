<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>REMAN ERP - Enterprise Resource Planning</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preload" href="HCLTech Roobert_font/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="HCLTech Roobert_font/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Medium.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="HCLTech Roobert_font/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Bold.woff2" as="font" type="font/woff2" crossorigin>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="header-container">
            <!-- Left Section: Logo -->
            <div class="header-left">
                <div class="logo-section">
                    <h1 class="logo">HCLSoftware | REMAN</h1>
                    <span class="logo-subtitle">Enterprise Resource Planning</span>
                </div>
            </div>

            <!-- Center Section: Redesigned Advanced Search Bar -->
            <div class="header-center">
                <div class="advanced-search-container">
                    <!-- Main Search Input -->
                    <div class="search-input-wrapper">
                        <!-- Search Type Selector -->
                        <div class="search-type-selector" id="searchTypeSelector">
                            <button class="search-type-btn" id="searchTypeBtn" aria-expanded="false" aria-haspopup="listbox">
                                <svg class="search-type-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                                    <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                <span class="search-type-text">All</span>
                                <svg class="search-type-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>

                            <!-- Enhanced Search Type Dropdown -->
                            <div class="search-type-dropdown enhanced-dropdown" id="searchTypeDropdown">
                                <!-- Search Filter for Dropdown -->
                                <div class="dropdown-search-container">
                                    <input type="text" class="dropdown-search-input" placeholder="Filter search types..." id="searchTypeFilter" />
                                    <svg class="dropdown-search-icon" width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                                        <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                </div>

                                <!-- Dropdown Options Container with Virtual Scrolling Support -->
                                <div class="dropdown-options-container virtual-scroll-container" id="searchTypeOptionsContainer">
                                    <div class="search-type-option active" data-type="all" data-icon="search" tabindex="0">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                                            <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        <span>All Items</span>
                                        <span class="option-count">(1,247)</span>
                                    </div>
                                    <div class="search-type-option" data-type="customers" data-icon="users" tabindex="0">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                            <circle cx="8.5" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                            <path d="M20 8v6M23 11h-6" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        <span>Customers</span>
                                        <span class="option-count">(324)</span>
                                    </div>
                                    <div class="search-type-option" data-type="parts" data-icon="package" tabindex="0">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M12 2L2 7l10 5 10-5-10-5z" stroke="currentColor" stroke-width="2"/>
                                            <path d="M2 17l10 5 10-5" stroke="currentColor" stroke-width="2"/>
                                            <path d="M2 12l10 5 10-5" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        <span>Parts</span>
                                        <span class="option-count">(1,247)</span>
                                    </div>
                                    <div class="search-type-option" data-type="transactions" data-icon="activity" tabindex="0">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <polyline points="22,12 18,12 15,21 9,3 6,12 2,12" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        <span>Transactions</span>
                                        <span class="option-count">(2,156)</span>
                                    </div>
                                    <div class="search-type-option" data-type="reports" data-icon="file-text" tabindex="0">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                                            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                                            <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                                            <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        <span>Reports</span>
                                        <span class="option-count">(89)</span>
                                    </div>
                                    <div class="search-type-option" data-type="suppliers" data-icon="truck" tabindex="0">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect x="1" y="3" width="15" height="13" stroke="currentColor" stroke-width="2"/>
                                            <polygon points="16,8 20,8 23,11 23,16 16,16" stroke="currentColor" stroke-width="2"/>
                                            <circle cx="5.5" cy="18.5" r="2.5" stroke="currentColor" stroke-width="2"/>
                                            <circle cx="18.5" cy="18.5" r="2.5" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        <span>Suppliers</span>
                                        <span class="option-count">(156)</span>
                                    </div>
                                    <div class="search-type-option" data-type="inventory" data-icon="box" tabindex="0">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" stroke="currentColor" stroke-width="2"/>
                                            <polyline points="3.27,6.96 12,12.01 20.73,6.96" stroke="currentColor" stroke-width="2"/>
                                            <line x1="12" y1="22.08" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        <span>Inventory</span>
                                        <span class="option-count">(923)</span>
                                    </div>
                                    <!-- Additional options for demonstration of large dataset -->
                                    <div class="search-type-option" data-type="vendors" data-icon="truck" tabindex="0">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <rect x="1" y="3" width="15" height="13" stroke="currentColor" stroke-width="2"/>
                                            <polygon points="16,8 20,8 23,11 23,16 16,16" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        <span>Vendors</span>
                                        <span class="option-count">(89)</span>
                                    </div>
                                    <div class="search-type-option" data-type="employees" data-icon="users" tabindex="0">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                            <circle cx="8.5" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        <span>Employees</span>
                                        <span class="option-count">(45)</span>
                                    </div>
                                    <div class="search-type-option" data-type="projects" data-icon="folder" tabindex="0">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        <span>Projects</span>
                                        <span class="option-count">(23)</span>
                                    </div>
                                    <div class="search-type-option" data-type="invoices" data-icon="file-text" tabindex="0">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                                            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        <span>Invoices</span>
                                        <span class="option-count">(567)</span>
                                    </div>
                                </div>

                                <!-- Enhanced Pagination Controls -->
                                <div class="dropdown-pagination" id="searchTypePagination" style="display: none;">
                                    <div class="pagination-info">
                                        <span id="searchTypePaginationInfo">Showing 1-11 of 11</span>
                                    </div>
                                    <div class="pagination-controls">
                                        <button class="pagination-btn" id="searchTypePrevBtn" disabled aria-label="Previous page">
                                            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <polyline points="15,18 9,12 15,6" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                        </button>
                                        <span class="pagination-pages" id="searchTypePaginationPages">1 / 1</span>
                                        <button class="pagination-btn" id="searchTypeNextBtn" disabled aria-label="Next page">
                                            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <polyline points="9,18 15,12 9,6" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="page-size-selector">
                                        <label for="searchTypePageSize" style="font-size: 0.75rem; color: var(--secondary-dark-cyan);">Show:</label>
                                        <select class="page-size-select" id="searchTypePageSize">
                                            <option value="5">5</option>
                                            <option value="10" selected>10</option>
                                            <option value="20">20</option>
                                            <option value="50">50</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- No Results Message -->
                                <div class="dropdown-no-results" id="searchTypeNoResults" style="display: none;">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                                        <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                    <p>No search types found</p>
                                    <button class="clear-filter-btn" id="clearSearchTypeFilter">Clear filter</button>
                                </div>
                            </div>
                        </div>

                        <!-- Search Input Field -->
                        <input
                            type="text"
                            class="advanced-search-input"
                            placeholder="Search across all modules..."
                            id="headerAdvancedSearch"
                            aria-label="Advanced search input"
                            aria-expanded="false"
                            aria-haspopup="listbox"
                            autocomplete="off"
                        />

                        <!-- Search Actions -->
                        <div class="search-actions">
                            <!-- Voice Search Button -->
                            <button class="search-action-btn voice-search-btn" id="voiceSearchBtn" aria-label="Voice search" title="Voice search">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z" stroke="currentColor" stroke-width="2"/>
                                    <path d="M19 10v2a7 7 0 0 1-14 0v-2" stroke="currentColor" stroke-width="2"/>
                                    <line x1="12" y1="19" x2="12" y2="23" stroke="currentColor" stroke-width="2"/>
                                    <line x1="8" y1="23" x2="16" y2="23" stroke="currentColor" stroke-width="2"/>
                                </svg>
                            </button>

                            <!-- Advanced Filters Button -->
                            <button class="search-action-btn filters-btn" id="filtersBtn" aria-label="Advanced filters" title="Advanced filters">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46" stroke="currentColor" stroke-width="2"/>
                                </svg>
                            </button>

                            <!-- Clear Search Button -->
                            <button class="search-action-btn clear-search-btn" id="clearSearchBtn" aria-label="Clear search" title="Clear search" style="display: none;">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                                    <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                                </svg>
                            </button>

                            <!-- Search Submit Button -->
                            <button class="search-submit-btn" id="searchSubmitBtn" aria-label="Search" title="Search">
                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                                    <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Advanced Search Dropdown -->
                    <div class="advanced-search-dropdown" id="advancedSearchDropdown">
                        <!-- Search Status Bar -->
                        <div class="search-status-bar">
                            <div class="search-status-info">
                                <span class="search-status-text" id="searchStatusText">Start typing to search...</span>
                                <span class="search-results-count" id="searchResultsCount" style="display: none;"></span>
                            </div>
                            <div class="search-keyboard-shortcuts">
                                <span class="keyboard-shortcut">
                                    <kbd>↑</kbd><kbd>↓</kbd> Navigate
                                </span>
                                <span class="keyboard-shortcut">
                                    <kbd>Enter</kbd> Select
                                </span>
                                <span class="keyboard-shortcut">
                                    <kbd>Esc</kbd> Close
                                </span>
                            </div>
                        </div>

                        <!-- Search Results Container -->
                        <div class="search-results-container" id="searchResultsContainer">
                            <!-- Quick Actions Section -->
                            <div class="search-section quick-actions-section">
                                <h4 class="search-section-title">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                        <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                    Quick Actions
                                </h4>
                                <div class="search-results-list">
                                    <a href="#" class="search-result-item quick-action" data-action="new-customer">
                                        <div class="result-icon">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                                <circle cx="8.5" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                                <line x1="20" y1="8" x2="20" y2="14" stroke="currentColor" stroke-width="2"/>
                                                <line x1="17" y1="11" x2="23" y2="11" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                        </div>
                                        <div class="result-content">
                                            <div class="result-title">Add New Customer</div>
                                            <div class="result-description">Create a new customer record</div>
                                        </div>
                                        <div class="result-shortcut">
                                            <kbd>Ctrl</kbd><kbd>N</kbd>
                                        </div>
                                    </a>
                                    <a href="#" class="search-result-item quick-action" data-action="new-transaction">
                                        <div class="result-icon">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <line x1="12" y1="1" x2="12" y2="23" stroke="currentColor" stroke-width="2"/>
                                                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                        </div>
                                        <div class="result-content">
                                            <div class="result-title">New Transaction</div>
                                            <div class="result-description">Create a new transaction</div>
                                        </div>
                                        <div class="result-shortcut">
                                            <kbd>Ctrl</kbd><kbd>T</kbd>
                                        </div>
                                    </a>
                                    <a href="#" class="search-result-item quick-action" data-action="generate-report">
                                        <div class="result-icon">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                                                <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                                                <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                                                <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                        </div>
                                        <div class="result-content">
                                            <div class="result-title">Generate Report</div>
                                            <div class="result-description">Create custom reports</div>
                                        </div>
                                        <div class="result-shortcut">
                                            <kbd>Ctrl</kbd><kbd>R</kbd>
                                        </div>
                                    </a>
                                </div>
                            </div>

                            <!-- Recent Searches Section -->
                            <div class="search-section recent-searches-section">
                                <h4 class="search-section-title">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                        <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                    Recent Searches
                                </h4>
                                <div class="search-results-list">
                                    <a href="#" class="search-result-item recent-search" data-search="customer ABC Corp">
                                        <div class="result-icon">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                                <circle cx="8.5" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                        </div>
                                        <div class="result-content">
                                            <div class="result-title">customer ABC Corp</div>
                                            <div class="result-description">Customer search • 2 minutes ago</div>
                                        </div>
                                    </a>
                                    <a href="#" class="search-result-item recent-search" data-search="parts inventory">
                                        <div class="result-icon">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M12 2L2 7l10 5 10-5-10-5z" stroke="currentColor" stroke-width="2"/>
                                                <path d="M2 17l10 5 10-5" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                        </div>
                                        <div class="result-content">
                                            <div class="result-title">parts inventory</div>
                                            <div class="result-description">Parts search • 15 minutes ago</div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Search Footer -->
                        <div class="search-dropdown-footer">
                            <div class="search-footer-left">
                                <span class="search-tip">💡 Tip: Use quotes for exact matches</span>
                            </div>
                            <div class="search-footer-right">
                                <button class="advanced-search-link" id="advancedSearchLink">
                                    Advanced Search
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M7 17L17 7" stroke="currentColor" stroke-width="2"/>
                                        <path d="M7 7h10v10" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Section: Icons and User Actions -->
            <div class="header-right">
                <!-- Notification Button -->
                <div class="notification-container">
                    <button class="notification-btn" id="notificationBtn" aria-label="Notifications" aria-expanded="false">
                        <svg class="notification-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 8C18 6.4087 17.3679 4.88258 16.2426 3.75736C15.1174 2.63214 13.5913 2 12 2C10.4087 2 8.88258 2.63214 7.75736 3.75736C6.63214 4.88258 6 6.4087 6 8C6 15 3 17 3 17H21C21 17 18 15 18 8Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M13.73 21C13.5542 21.3031 13.3019 21.5547 12.9982 21.7295C12.6946 21.9044 12.3504 21.9965 12 21.9965C11.6496 21.9965 11.3054 21.9044 11.0018 21.7295C10.6981 21.5547 10.4458 21.3031 10.27 21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span class="notification-badge" id="notificationBadge">3</span>
                    </button>

                    <!-- Notification Dropdown -->
                    <div class="notification-dropdown" id="notificationDropdown">
                        <div class="dropdown-header">
                            <h3>Notifications</h3>
                            <button class="mark-all-read" id="markAllRead">Mark all as read</button>
                        </div>
                        <div class="notification-list">
                            <div class="notification-item unread">
                                <div class="notification-content">
                                    <p class="notification-text">New purchase order #PO-2024-001 requires approval</p>
                                    <span class="notification-time">2 minutes ago</span>
                                </div>
                            </div>
                            <div class="notification-item unread">
                                <div class="notification-content">
                                    <p class="notification-text">Inventory level for Core Part #CP-5521 is below threshold</p>
                                    <span class="notification-time">15 minutes ago</span>
                                </div>
                            </div>
                            <div class="notification-item">
                                <div class="notification-content">
                                    <p class="notification-text">Monthly report has been generated successfully</p>
                                    <span class="notification-time">1 hour ago</span>
                                </div>
                            </div>
                        </div>
                        <div class="dropdown-footer">
                            <a href="#notifications" class="view-all-link">View all notifications</a>
                        </div>
                    </div>
                </div>

                <!-- Profile Button -->
                <div class="profile-container">
                    <button class="profile-btn" id="profileBtn" aria-label="User profile menu" aria-expanded="false">
                        <div class="profile-avatar">
                            <svg class="profile-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>

                    <!-- Profile Dropdown -->
                    <div class="profile-dropdown" id="profileDropdown">
                        <div class="dropdown-header">
                            <div class="user-info">
                                <div class="user-avatar">
                                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="user-details">
                                    <p class="user-name">John Doe</p>
                                    <p class="user-role">System Administrator</p>
                                </div>
                            </div>
                        </div>
                        <div class="profile-menu">
                            <a href="#profile" class="profile-menu-item">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                My Profile
                            </a>
                            <a href="#settings" class="profile-menu-item">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                    <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                Settings
                            </a>
                            <a href="#help" class="profile-menu-item">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                    <path d="M9.09 9C9.3251 8.33167 9.78915 7.76811 10.4 7.40913C11.0108 7.05016 11.7289 6.91894 12.4272 7.03871C13.1255 7.15849 13.7588 7.52152 14.2151 8.06353C14.6713 8.60553 14.9211 9.29152 14.92 10C14.92 12 11.92 13 11.92 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M12 17H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                Help
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Logout Button -->
                <button class="logout-btn" id="logoutBtn" aria-label="Logout">
                    <svg class="logout-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <polyline points="16,17 21,12 16,7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <line x1="21" y1="12" x2="9" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <span>Logout</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Navigation Section -->
    <nav class="navigation-bar">
        <div class="nav-container">
            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" id="mobileMenuToggle" aria-label="Toggle navigation menu">
                <span></span>
                <span></span>
                <span></span>
            </button>

            <!-- Main Navigation -->
            <div class="main-nav" id="mainNav">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="#records" class="nav-link active" data-section="records">Records</a>
                    </li>
                    <li class="nav-item">
                        <a href="#edit" class="nav-link" data-section="edit">Edit</a>
                    </li>
                    <li class="nav-item nav-item-dropdown" id="mastersNavItem">
                        <a href="#masters" class="nav-link" data-section="masters" id="mastersNavLink">
                            Masters
                            <svg class="nav-dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </a>

                        <!-- Masters Dropdown Menu -->
                        <div class="masters-dropdown" id="mastersDropdown">
                            <div class="masters-dropdown-header">
                                <div class="masters-search-container">
                                    <div class="masters-search-wrapper">
                                        <svg class="masters-search-icon" width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <input
                                            type="text"
                                            class="masters-search-input"
                                            placeholder="Search masters..."
                                            id="mastersSearchInput"
                                            aria-label="Search masters"
                                        />
                                        <button class="masters-search-clear" id="mastersSearchClear" aria-label="Clear search" style="display: none;">
                                            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="masters-dropdown-content">
                                <div class="masters-dropdown-grid" id="mastersDropdownGrid">
                                    <a href="#bin-location" class="masters-dropdown-item" data-submenu="bin-location">
                                        <span class="masters-item-text">Bin Location</span>
                                    </a>
                                    <a href="#category" class="masters-dropdown-item" data-submenu="category">
                                        <span class="masters-item-text">Category</span>
                                    </a>
                                    <a href="#sac-hsn" class="masters-dropdown-item" data-submenu="sac-hsn">
                                        <span class="masters-item-text">SAC & HSN / Custom Tariff Master</span>
                                    </a>
                                    <a href="#supplier-order-class" class="masters-dropdown-item" data-submenu="supplier-order-class">
                                        <span class="masters-item-text">Supplier Order Class</span>
                                    </a>
                                    <a href="#parts" class="masters-dropdown-item" data-submenu="parts">
                                        <span class="masters-item-text">Parts</span>
                                    </a>
                                    <a href="#supplier" class="masters-dropdown-item" data-submenu="supplier">
                                        <span class="masters-item-text">Supplier</span>
                                    </a>
                                    <a href="#clearing-agent" class="masters-dropdown-item" data-submenu="clearing-agent">
                                        <span class="masters-item-text">Clearing Agent</span>
                                    </a>
                                    <a href="#dealer" class="masters-dropdown-item" data-submenu="dealer">
                                        <span class="masters-item-text">Dealer</span>
                                    </a>
                                    <a href="#service-technician" class="masters-dropdown-item" data-submenu="service-technician">
                                        <span class="masters-item-text">Service Technician</span>
                                    </a>
                                    <a href="#renovator" class="masters-dropdown-item" data-submenu="renovator">
                                        <span class="masters-item-text">Renovator</span>
                                    </a>
                                    <a href="#customer-master" class="masters-dropdown-item" data-submenu="customer-master">
                                        <span class="masters-item-text">Customer Master</span>
                                    </a>
                                    <a href="#operation" class="masters-dropdown-item" data-submenu="operation">
                                        <span class="masters-item-text">Operation</span>
                                    </a>
                                    <a href="#machining-checklist" class="masters-dropdown-item" data-submenu="machining-checklist">
                                        <span class="masters-item-text">Machining Checklist</span>
                                    </a>
                                    <a href="#machining-checklist-vendor" class="masters-dropdown-item" data-submenu="machining-checklist-vendor">
                                        <span class="masters-item-text">Machining Checklist Vendor Association</span>
                                    </a>
                                </div>

                                <div class="masters-no-results" id="mastersNoResults" style="display: none;">
                                    <p>No masters found matching your search.</p>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a href="#transactions" class="nav-link" data-section="transactions">Transactions</a>
                    </li>
                    <li class="nav-item">
                        <a href="#status" class="nav-link" data-section="status">Status</a>
                    </li>
                    <li class="nav-item">
                        <a href="#reports" class="nav-link" data-section="reports">Reports</a>
                    </li>
                    <li class="nav-item">
                        <a href="#options" class="nav-link" data-section="options">Options</a>
                    </li>
                    <li class="nav-item">
                        <a href="#window" class="nav-link" data-section="window">Window</a>
                    </li>
                    <li class="nav-item">
                        <a href="#help" class="nav-link" data-section="help">Help</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content Area -->
    <main class="main-content">
        <!-- Records Section -->
        <section id="records" class="content-section active">
            <div class="section-header">
                <h2 class="section-title">Records Management</h2>
                <p class="section-description">Manage and view all system records</p>
            </div>
            <div class="content-grid">
                <div class="card">
                    <h3 class="card-title">Customer Records</h3>
                    <p class="card-description">View and manage customer information</p>
                    <button class="btn btn-primary">View Records</button>
                </div>
                <div class="card">
                    <h3 class="card-title">Product Records</h3>
                    <p class="card-description">Manage product catalog and inventory</p>
                    <button class="btn btn-primary">View Records</button>
                </div>
                <div class="card">
                    <h3 class="card-title">Supplier Records</h3>
                    <p class="card-description">Maintain supplier database</p>
                    <button class="btn btn-primary">View Records</button>
                </div>
                <div class="card">
                    <h3 class="card-title">Employee Records</h3>
                    <p class="card-description">Human resources management</p>
                    <button class="btn btn-primary">View Records</button>
                </div>
            </div>
        </section>

        <!-- Edit Section -->
        <section id="edit" class="content-section">
            <div class="section-header">
                <h2 class="section-title">Edit Operations</h2>
                <p class="section-description">Modify and update system data</p>
            </div>

            <!-- Test Radio Button Section -->
            <div class="card" style="margin-bottom: 2rem;">
                <h3 class="card-title">Radio Button Test</h3>
                <p class="card-description">Testing fixed radio button functionality</p>

                <div class="radio-group">
                    <label>Active Status</label>
                    <div class="radio-options">
                        <label class="radio-label">
                            <input type="radio" name="activeStatus" value="yes" checked>
                            <span class="radio-custom"></span>
                            Yes
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="activeStatus" value="no">
                            <span class="radio-custom"></span>
                            No
                        </label>
                    </div>
                </div>

                <div class="radio-group" style="margin-top: 1rem;">
                    <label>Priority Level</label>
                    <div class="radio-options">
                        <label class="radio-label">
                            <input type="radio" name="priority" value="low">
                            <span class="radio-custom"></span>
                            Low
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="priority" value="medium" checked>
                            <span class="radio-custom"></span>
                            Medium
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="priority" value="high">
                            <span class="radio-custom"></span>
                            High
                        </label>
                    </div>
                </div>
            </div>

            <div class="content-grid">
                <div class="card">
                    <h3 class="card-title">Edit Customer Data</h3>
                    <p class="card-description">Update customer information and preferences</p>
                    <button class="btn btn-secondary">Edit Data</button>
                </div>
                <div class="card">
                    <h3 class="card-title">Edit Product Information</h3>
                    <p class="card-description">Modify product details and specifications</p>
                    <button class="btn btn-secondary">Edit Data</button>
                </div>
                <div class="card">
                    <h3 class="card-title">Edit Pricing</h3>
                    <p class="card-description">Update pricing structures and discounts</p>
                    <button class="btn btn-secondary">Edit Data</button>
                </div>
            </div>
        </section>

        <!-- Masters Section -->
        <section id="masters" class="content-section">
            <!-- Masters Content Area -->
            <div class="masters-content">
                <!-- Bin Location Content -->
                <div id="bin-location" class="submenu-content active">
                    <div class="submenu-header">
                        <h3 class="submenu-title">Bin Location Master</h3>
                        <p class="submenu-description">Configure warehouse bin locations and storage areas</p>
                    </div>
                    <div class="submenu-actions">
                        <button class="btn btn-primary">Add New Bin Location</button>
                        <button class="btn btn-secondary">View All Locations</button>
                        <button class="btn btn-outline">Import Locations</button>
                    </div>
                </div>

                <!-- Category Content -->
                <div id="category" class="submenu-content">
                    <div class="submenu-header">
                        <h3 class="submenu-title">Category Master</h3>
                        <p class="submenu-description">Manage product categories and classifications</p>
                    </div>
                    <div class="submenu-actions">
                        <button class="btn btn-primary">Add New Category</button>
                        <button class="btn btn-secondary">View All Categories</button>
                        <button class="btn btn-outline">Category Hierarchy</button>
                    </div>
                </div>

                <!-- SAC & HSN Content -->
                <div id="sac-hsn" class="submenu-content">
                    <div class="submenu-header">
                        <h3 class="submenu-title">SAC & HSN / Custom Tariff Master</h3>
                        <p class="submenu-description">Configure SAC codes, HSN codes, and custom tariff classifications</p>
                    </div>
                    <div class="submenu-actions">
                        <button class="btn btn-primary">Add New Code</button>
                        <button class="btn btn-secondary">View All Codes</button>
                        <button class="btn btn-outline">Import Codes</button>
                    </div>
                </div>

                <!-- Supplier Order Class Content -->
                <div id="supplier-order-class" class="submenu-content">
                    <div class="submenu-header">
                        <h3 class="submenu-title">Supplier Order Class</h3>
                        <p class="submenu-description">Define supplier order classifications and categories</p>
                    </div>
                    <div class="submenu-actions">
                        <button class="btn btn-primary">Add New Class</button>
                        <button class="btn btn-secondary">View All Classes</button>
                        <button class="btn btn-outline">Class Settings</button>
                    </div>
                </div>

                <!-- Parts Content -->
                <div id="parts" class="submenu-content">
                    <div class="parts-master">
                        <!-- Header -->
                        <div class="parts-header">
                            <div class="parts-title-section">
                                <h3 class="submenu-title">Parts Master</h3>
                                <p class="submenu-description">Unified parts management system for all part categories</p>
                            </div>
                        </div>

                        <!-- Welcome Section - Always visible at top -->
                        <div class="parts-welcome-section">
                            <div class="welcome-content">
                                <h4>Welcome to Parts Management</h4>
                                <p>Select a part category below to view records or add new parts.</p>
                                <div class="welcome-stats-container">
                                    <div class="stat-item stat-total">
                                        <span class="stat-number">1,247</span>
                                        <span class="stat-label">Total Parts</span>
                                    </div>
                                    <div class="stat-item stat-new">
                                        <span class="stat-number">324</span>
                                        <span class="stat-label">New Parts</span>
                                    </div>
                                    <div class="stat-item stat-salvage">
                                        <span class="stat-number">156</span>
                                        <span class="stat-label">Salvage Parts</span>
                                    </div>
                                    <div class="stat-item stat-core">
                                        <span class="stat-number">89</span>
                                        <span class="stat-label">Core Parts</span>
                                    </div>
                                    <div class="stat-item stat-exchange">
                                        <span class="stat-number">678</span>
                                        <span class="stat-label">Exchange Parts</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Parts Categories Grid -->
                        <div class="parts-categories-grid">
                            <!-- New Parts Category -->
                            <div class="parts-category-card" data-category="new-parts">
                                <div class="category-header">
                                    <div class="category-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M12 2L2 7l10 5 10-5-10-5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M2 17l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M2 12l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <h4 class="category-title">New Parts</h4>
                                    <p class="category-description">Manage new spare parts inventory</p>
                                </div>
                                <div class="category-actions">
                                    <button class="btn btn-primary category-add-btn" data-category="new-parts">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
                                            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        Add New
                                    </button>
                                    <button class="btn btn-secondary category-view-btn" data-category="new-parts">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        View Records
                                    </button>
                                    <button class="btn btn-outline category-import-export-btn" data-category="new-parts" onclick="importExportParts('new-parts')">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                                            <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                                            <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        Import/Export
                                    </button>
                                </div>
                            </div>

                            <!-- Salvage Parts Category -->
                            <div class="parts-category-card" data-category="salvage-parts">
                                <div class="category-header">
                                    <div class="category-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" stroke="currentColor" stroke-width="2"/>
                                            <rect x="8" y="2" width="8" height="4" rx="1" ry="1" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <h4 class="category-title">Salvage Parts</h4>
                                    <p class="category-description">Manage salvage parts and recovery processes</p>
                                </div>
                                <div class="category-actions">
                                    <button class="btn btn-primary category-add-btn" data-category="salvage-parts">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
                                            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        Add New
                                    </button>
                                    <button class="btn btn-secondary category-view-btn" data-category="salvage-parts">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        View Records
                                    </button>
                                    <button class="btn btn-outline category-import-export-btn" data-category="salvage-parts" onclick="importExportParts('salvage-parts')">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                                            <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                                            <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        Import/Export
                                    </button>
                                </div>
                            </div>

                            <!-- Core Parts Category -->
                            <div class="parts-category-card" data-category="core-parts">
                                <div class="category-header">
                                    <div class="category-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <h4 class="category-title">Core Parts</h4>
                                    <p class="category-description">Manage core parts for remanufacturing</p>
                                </div>
                                <div class="category-actions">
                                    <button class="btn btn-primary category-add-btn" data-category="core-parts">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
                                            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        Add New
                                    </button>
                                    <button class="btn btn-secondary category-view-btn" data-category="core-parts">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        View Records
                                    </button>
                                    <button class="btn btn-outline category-import-export-btn" data-category="core-parts" onclick="importExportParts('core-parts')">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                                            <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                                            <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        Import/Export
                                    </button>
                                </div>
                            </div>

                            <!-- Exchange Parts Category -->
                            <div class="parts-category-card" data-category="exchange-parts">
                                <div class="category-header">
                                    <div class="category-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                            <circle cx="8.5" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                            <polyline points="17,11 19,13 23,9" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <h4 class="category-title">Exchange Parts</h4>
                                    <p class="category-description">Manage exchange parts and replacement programs</p>
                                </div>
                                <div class="category-actions">
                                    <button class="btn btn-primary category-add-btn" data-category="exchange-parts">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
                                            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        Add New
                                    </button>
                                    <button class="btn btn-secondary category-view-btn" data-category="exchange-parts">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        View Records
                                    </button>
                                    <button class="btn btn-outline category-import-export-btn" data-category="exchange-parts" onclick="importExportParts('exchange-parts')">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                                            <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                                            <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                        Import/Export
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Dynamic Content Area for Forms and Views -->
                        <div class="parts-dynamic-content" id="partsDynamicContent">
                            <!-- Content will be dynamically loaded here based on user selection -->
                    </div>
                </div>


                <!-- Supplier Content -->
                <div id="supplier" class="submenu-content">
                    <div class="submenu-header">
                        <h3 class="submenu-title">Supplier Master</h3>
                        <p class="submenu-description">Manage supplier information and relationships</p>
                    </div>
                    <div class="submenu-actions">
                        <button class="btn btn-primary">Add New Supplier</button>
                        <button class="btn btn-secondary">View All Suppliers</button>
                        <button class="btn btn-outline">Supplier Performance</button>
                    </div>
                </div>

                <!-- Clearing Agent Content -->
                <div id="clearing-agent" class="submenu-content">
                    <div class="submenu-header">
                        <h3 class="submenu-title">Clearing Agent Master</h3>
                        <p class="submenu-description">Manage clearing agents and customs brokers</p>
                    </div>
                    <div class="submenu-actions">
                        <button class="btn btn-primary">Add New Agent</button>
                        <button class="btn btn-secondary">View All Agents</button>
                        <button class="btn btn-outline">Agent Performance</button>
                    </div>
                </div>

                <!-- Dealer Content -->
                <div id="dealer" class="submenu-content">
                    <div class="submenu-header">
                        <h3 class="submenu-title">Dealer Master</h3>
                        <p class="submenu-description">Manage dealer network and partnerships</p>
                    </div>
                    <div class="submenu-actions">
                        <button class="btn btn-primary">Add New Dealer</button>
                        <button class="btn btn-secondary">View All Dealers</button>
                        <button class="btn btn-outline">Dealer Network</button>
                    </div>
                </div>

                <!-- Service Technician Content -->
                <div id="service-technician" class="submenu-content">
                    <div class="submenu-header">
                        <h3 class="submenu-title">Service Technician Master</h3>
                        <p class="submenu-description">Manage service technicians and field personnel</p>
                    </div>
                    <div class="submenu-actions">
                        <button class="btn btn-primary">Add New Technician</button>
                        <button class="btn btn-secondary">View All Technicians</button>
                        <button class="btn btn-outline">Technician Schedule</button>
                    </div>
                </div>

                <!-- Renovator Content -->
                <div id="renovator" class="submenu-content">
                    <div class="submenu-header">
                        <h3 class="submenu-title">Renovator Master</h3>
                        <p class="submenu-description">Manage renovation specialists and processes</p>
                    </div>
                    <div class="submenu-actions">
                        <button class="btn btn-primary">Add New Renovator</button>
                        <button class="btn btn-secondary">View All Renovators</button>
                        <button class="btn btn-outline">Renovation Projects</button>
                    </div>
                </div>

                <!-- Customer Master Content -->
                <div id="customer-master" class="submenu-content">
                    <div class="submenu-header">
                        <h3 class="submenu-title">Customer Master</h3>
                        <p class="submenu-description">Comprehensive customer information management</p>
                    </div>
                    <div class="submenu-actions">
                        <button class="btn btn-primary">Add New Customer</button>
                        <button class="btn btn-secondary">View All Customers</button>
                        <button class="btn btn-outline">Customer Analytics</button>
                    </div>
                </div>

                <!-- Operation Content -->
                <div id="operation" class="submenu-content">
                    <div class="submenu-header">
                        <h3 class="submenu-title">Operation Master</h3>
                        <p class="submenu-description">Define operational procedures and workflows</p>
                    </div>
                    <div class="submenu-actions">
                        <button class="btn btn-primary">Add New Operation</button>
                        <button class="btn btn-secondary">View All Operations</button>
                        <button class="btn btn-outline">Operation Workflows</button>
                    </div>
                </div>

                <!-- Machining Checklist Content -->
                <div id="machining-checklist" class="submenu-content">
                    <div class="submenu-header">
                        <h3 class="submenu-title">Machining Checklist</h3>
                        <p class="submenu-description">Manage machining quality control checklists</p>
                    </div>
                    <div class="submenu-actions">
                        <button class="btn btn-primary">Create New Checklist</button>
                        <button class="btn btn-secondary">View All Checklists</button>
                        <button class="btn btn-outline">Checklist Templates</button>
                    </div>
                </div>

                <!-- Machining Checklist Vendor Association Content -->
                <div id="machining-checklist-vendor" class="submenu-content">
                    <div class="submenu-header">
                        <h3 class="submenu-title">Machining Checklist Vendor Association</h3>
                        <p class="submenu-description">Associate machining checklists with vendors and suppliers</p>
                    </div>
                    <div class="submenu-actions">
                        <button class="btn btn-primary">Create Association</button>
                        <button class="btn btn-secondary">View All Associations</button>
                        <button class="btn btn-outline">Vendor Compliance</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Transactions Section -->
        <section id="transactions" class="content-section">
            <div class="section-header">
                <h2 class="section-title">Transactions</h2>
                <p class="section-description">Process business transactions and operations</p>
            </div>
            <div class="content-grid">
                <div class="card">
                    <h3 class="card-title">Sales Orders</h3>
                    <p class="card-description">Create and manage sales orders</p>
                    <button class="btn btn-primary">New Transaction</button>
                </div>
                <div class="card">
                    <h3 class="card-title">Purchase Orders</h3>
                    <p class="card-description">Process purchase orders and requisitions</p>
                    <button class="btn btn-primary">New Transaction</button>
                </div>
                <div class="card">
                    <h3 class="card-title">Inventory Transfers</h3>
                    <p class="card-description">Manage stock movements and transfers</p>
                    <button class="btn btn-primary">New Transaction</button>
                </div>
                <div class="card">
                    <h3 class="card-title">Financial Entries</h3>
                    <p class="card-description">Record journal entries and adjustments</p>
                    <button class="btn btn-primary">New Transaction</button>
                </div>
            </div>
        </section>

        <!-- Status Section -->
        <section id="status" class="content-section">
            <div class="section-header">
                <h2 class="section-title">System Status</h2>
                <p class="section-description">Monitor system performance and status</p>
            </div>
            <div class="status-dashboard">
                <div class="status-card">
                    <h3 class="status-title">System Health</h3>
                    <div class="status-indicator status-good">Operational</div>
                    <p class="status-details">All systems running normally</p>
                </div>
                <div class="status-card">
                    <h3 class="status-title">Database Status</h3>
                    <div class="status-indicator status-good">Connected</div>
                    <p class="status-details">Database connection stable</p>
                </div>
                <div class="status-card">
                    <h3 class="status-title">Active Users</h3>
                    <div class="status-number">24</div>
                    <p class="status-details">Users currently online</p>
                </div>
                <div class="status-card">
                    <h3 class="status-title">Pending Tasks</h3>
                    <div class="status-number">7</div>
                    <p class="status-details">Tasks awaiting approval</p>
                </div>
            </div>
        </section>

        <!-- Reports Section -->
        <section id="reports" class="content-section">
            <div class="section-header">
                <h2 class="section-title">Reports & Analytics</h2>
                <p class="section-description">Generate reports and view analytics</p>
            </div>
            <div class="content-grid">
                <div class="card">
                    <h3 class="card-title">Financial Reports</h3>
                    <p class="card-description">P&L, Balance Sheet, Cash Flow statements</p>
                    <button class="btn btn-secondary">Generate Report</button>
                </div>
                <div class="card">
                    <h3 class="card-title">Sales Analytics</h3>
                    <p class="card-description">Sales performance and trend analysis</p>
                    <button class="btn btn-secondary">Generate Report</button>
                </div>
                <div class="card">
                    <h3 class="card-title">Inventory Reports</h3>
                    <p class="card-description">Stock levels and movement reports</p>
                    <button class="btn btn-secondary">Generate Report</button>
                </div>
                <div class="card">
                    <h3 class="card-title">Custom Reports</h3>
                    <p class="card-description">Build custom reports and dashboards</p>
                    <button class="btn btn-secondary">Generate Report</button>
                </div>
            </div>
        </section>

        <!-- Options Section -->
        <section id="options" class="content-section">
            <div class="section-header">
                <h2 class="section-title">System Options</h2>
                <p class="section-description">Configure system preferences and settings</p>
            </div>
            <div class="options-grid">
                <div class="option-group">
                    <h3 class="option-title">User Preferences</h3>
                    <div class="option-item">
                        <label class="option-label">Theme</label>
                        <select class="option-select">
                            <option>Light</option>
                            <option>Dark</option>
                            <option>Auto</option>
                        </select>
                    </div>
                    <div class="option-item">
                        <label class="option-label">Language</label>
                        <select class="option-select">
                            <option>English</option>
                            <option>Spanish</option>
                            <option>French</option>
                        </select>
                    </div>
                </div>
                <div class="option-group">
                    <h3 class="option-title">System Settings</h3>
                    <div class="option-item">
                        <label class="option-label">Auto-save</label>
                        <input type="checkbox" class="option-checkbox" checked>
                    </div>
                    <div class="option-item">
                        <label class="option-label">Notifications</label>
                        <input type="checkbox" class="option-checkbox" checked>
                    </div>
                </div>
            </div>
        </section>

        <!-- Window Section -->
        <section id="window" class="content-section">
            <div class="section-header">
                <h2 class="section-title">Window Management</h2>
                <p class="section-description">Manage application windows and layouts</p>
            </div>
            <div class="window-controls">
                <button class="btn btn-outline">New Window</button>
                <button class="btn btn-outline">Tile Windows</button>
                <button class="btn btn-outline">Cascade Windows</button>
                <button class="btn btn-outline">Close All</button>
            </div>
        </section>

        <!-- Help Section -->
        <section id="help" class="content-section">
            <div class="section-header">
                <h2 class="section-title">Help & Support</h2>
                <p class="section-description">Get assistance and documentation</p>
            </div>
            <div class="help-grid">
                <div class="help-card">
                    <h3 class="help-title">User Manual</h3>
                    <p class="help-description">Comprehensive user guide and documentation</p>
                    <button class="btn btn-tertiary">View Manual</button>
                </div>
                <div class="help-card">
                    <h3 class="help-title">Video Tutorials</h3>
                    <p class="help-description">Step-by-step video guides</p>
                    <button class="btn btn-tertiary">Watch Videos</button>
                </div>
                <div class="help-card">
                    <h3 class="help-title">Contact Support</h3>
                    <p class="help-description">Get help from our support team</p>
                    <button class="btn btn-tertiary">Contact Us</button>
                </div>
                <div class="help-card">
                    <h3 class="help-title">FAQ</h3>
                    <p class="help-description">Frequently asked questions</p>
                    <button class="btn btn-tertiary">View FAQ</button>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-content">
                <p class="footer-text">&copy; 2024 REMAN ERP. All rights reserved.</p>
                <p class="footer-version">Version 1.0.0</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>